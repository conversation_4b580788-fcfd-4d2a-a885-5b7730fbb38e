import { AppConfig } from '../types';

// App Configuration
export const APP_CONFIG: AppConfig = {
  // Replace with your actual Google Drive folder ID
  // This should be the ID of the shared folder containing semester folders
  googleDriveFolderId: 'YOUR_GOOGLE_DRIVE_FOLDER_ID',
  
  // Firebase configuration - Replace with your actual Firebase config
  firebaseConfig: {
    apiKey: 'YOUR_FIREBASE_API_KEY',
    authDomain: 'YOUR_PROJECT_ID.firebaseapp.com',
    projectId: 'YOUR_PROJECT_ID',
    storageBucket: 'YOUR_PROJECT_ID.appspot.com',
    messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
    appId: 'YOUR_APP_ID',
  },
};

// Google Drive API configuration
export const GOOGLE_DRIVE_CONFIG = {
  scopes: [
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
  ],
  discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
};

// App theme colors
export const COLORS = {
  primary: '#6366F1', // Indigo
  secondary: '#8B5CF6', // Purple
  accent: '#06B6D4', // Cyan
  background: '#FFFFFF',
  surface: '#F8FAFC',
  text: {
    primary: '#1E293B',
    secondary: '#64748B',
    light: '#94A3B8',
  },
  border: '#E2E8F0',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  gray: {
    50: '#F8FAFC',
    100: '#F1F5F9',
    200: '#E2E8F0',
    300: '#CBD5E1',
    400: '#94A3B8',
    500: '#64748B',
    600: '#475569',
    700: '#334155',
    800: '#1E293B',
    900: '#0F172A',
  },
};

// App constants
export const CONSTANTS = {
  APP_NAME: 'NoteNest',
  VERSION: '1.0.0',
  SUPPORTED_FILE_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
  ],
  MAX_SEARCH_RESULTS: 50,
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
};

// File type mappings
export const FILE_TYPE_ICONS = {
  'application/pdf': 'file-pdf-o',
  'application/msword': 'file-word-o',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'file-word-o',
  'application/vnd.ms-powerpoint': 'file-powerpoint-o',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'file-powerpoint-o',
  'text/plain': 'file-text-o',
  default: 'file-o',
};

export const FILE_TYPE_COLORS = {
  'application/pdf': '#DC2626',
  'application/msword': '#2563EB',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '#2563EB',
  'application/vnd.ms-powerpoint': '#EA580C',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': '#EA580C',
  'text/plain': '#059669',
  default: '#6B7280',
};
