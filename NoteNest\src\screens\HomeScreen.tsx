import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { Semester, User } from '../types';
import DriveApiService from '../services/driveApi';
import AuthService from '../services/auth';
import { COLORS, CONSTANTS } from '../config/app';

interface HomeScreenProps {
  navigation: any;
  user: User;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation, user }) => {
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const driveService = DriveApiService.getInstance();
  const authService = AuthService.getInstance();

  const loadSemesters = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const semesterData = await driveService.getSemesterFolders();
      setSemesters(semesterData);
    } catch (error) {
      console.error('Error loading semesters:', error);
      setError('Failed to load semesters. Please check your internet connection.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadSemesters();
    }, [])
  );

  const handleRefresh = () => {
    loadSemesters(true);
  };

  const handleSemesterPress = (semester: Semester) => {
    navigation.navigate('Semester', { semester });
  };

  const handleSearchPress = () => {
    navigation.navigate('Search');
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await authService.signOut();
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderSemesterCard = ({ item }: { item: Semester }) => (
    <TouchableOpacity
      style={styles.semesterCard}
      onPress={() => handleSemesterPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.semesterIcon}>
        <Ionicons name="folder" size={32} color={COLORS.primary} />
      </View>
      <View style={styles.semesterInfo}>
        <Text style={styles.semesterName}>{item.name}</Text>
        <Text style={styles.fileCount}>
          {item.fileCount} {item.fileCount === 1 ? 'file' : 'files'}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={COLORS.text.light} />
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <View style={styles.userInfo}>
          <Text style={styles.greeting}>Hello,</Text>
          <Text style={styles.userName}>{user.name?.split(' ')[0] || 'Student'}</Text>
        </View>
        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Ionicons name="log-out-outline" size={24} color={COLORS.text.secondary} />
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity style={styles.searchBar} onPress={handleSearchPress}>
        <Ionicons name="search" size={20} color={COLORS.text.light} />
        <Text style={styles.searchPlaceholder}>Search notes...</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="folder-open-outline" size={64} color={COLORS.text.light} />
      <Text style={styles.emptyTitle}>No Semesters Found</Text>
      <Text style={styles.emptyDescription}>
        Make sure your Google Drive folder contains semester folders with notes.
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => loadSemesters()}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="alert-circle-outline" size={64} color={COLORS.error} />
      <Text style={styles.emptyTitle}>Error Loading Data</Text>
      <Text style={styles.emptyDescription}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => loadSemesters()}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading semesters...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />
      
      {renderHeader()}
      
      <View style={styles.content}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Semesters</Text>
          <Text style={styles.sectionSubtitle}>
            {semesters.length} {semesters.length === 1 ? 'semester' : 'semesters'} available
          </Text>
        </View>

        {error ? (
          renderErrorState()
        ) : semesters.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={semesters}
            renderItem={renderSemesterCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[COLORS.primary]}
                tintColor={COLORS.primary}
              />
            }
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: COLORS.background,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  userInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text.primary,
  },
  signOutButton: {
    padding: 8,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  searchPlaceholder: {
    marginLeft: 12,
    fontSize: 16,
    color: COLORS.text.light,
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.text.secondary,
  },
  listContainer: {
    paddingBottom: 20,
  },
  semesterCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  semesterIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  semesterInfo: {
    flex: 1,
  },
  semesterName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  fileCount: {
    fontSize: 14,
    color: COLORS.text.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.background,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default HomeScreen;
