import AsyncStorage from '@react-native-async-storage/async-storage';
import { DriveFile, Semester } from '../types';
import { APP_CONFIG, CONSTANTS } from '../config/app';
import AuthService from './auth';

class DriveApiService {
  private static instance: DriveApiService;
  private baseUrl = 'https://www.googleapis.com/drive/v3';
  
  public static getInstance(): DriveApiService {
    if (!DriveApiService.instance) {
      DriveApiService.instance = new DriveApiService();
    }
    return DriveApiService.instance;
  }

  // Get authorization headers
  private async getAuthHeaders(): Promise<{ [key: string]: string }> {
    const accessToken = await AuthService.getInstance().getStoredAccessToken();
    if (!accessToken) {
      throw new Error('No access token available');
    }
    
    return {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  // Get semester folders from the main shared folder
  async getSemesterFolders(): Promise<Semester[]> {
    try {
      const headers = await this.getAuthHeaders();
      const folderId = APP_CONFIG.googleDriveFolderId;
      
      // First, get all folders inside the main folder
      const response = await fetch(
        `${this.baseUrl}/files?q='${folderId}'+in+parents+and+mimeType='application/vnd.google-apps.folder'&fields=files(id,name,modifiedTime)&orderBy=name`,
        { headers }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to fetch folders: ${response.statusText}`);
      }
      
      const data = await response.json();
      const folders = data.files || [];
      
      // Get file count for each semester folder
      const semesters: Semester[] = await Promise.all(
        folders.map(async (folder: any) => {
          const fileCount = await this.getFileCountInFolder(folder.id);
          return {
            id: folder.id,
            name: folder.name,
            fileCount,
            files: [], // Will be loaded when needed
          };
        })
      );
      
      // Cache the result
      await this.cacheSemesters(semesters);
      
      return semesters;
    } catch (error) {
      console.error('Error fetching semester folders:', error);
      // Try to return cached data if available
      return await this.getCachedSemesters() || [];
    }
  }

  // Get files in a specific semester folder
  async getFilesInSemester(semesterId: string): Promise<DriveFile[]> {
    try {
      const headers = await this.getAuthHeaders();
      
      // Build query to get files in the semester folder
      const query = `'${semesterId}'+in+parents+and+mimeType!='application/vnd.google-apps.folder'`;
      const fields = 'files(id,name,mimeType,size,modifiedTime,webViewLink,thumbnailLink)';
      
      const response = await fetch(
        `${this.baseUrl}/files?q=${encodeURIComponent(query)}&fields=${fields}&orderBy=name`,
        { headers }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to fetch files: ${response.statusText}`);
      }
      
      const data = await response.json();
      const files = data.files || [];
      
      // Filter supported file types and map to DriveFile interface
      const driveFiles: DriveFile[] = files
        .filter((file: any) => this.isSupportedFileType(file.mimeType))
        .map((file: any) => ({
          id: file.id,
          name: file.name,
          mimeType: file.mimeType,
          size: file.size,
          modifiedTime: file.modifiedTime,
          webViewLink: file.webViewLink,
          thumbnailLink: file.thumbnailLink,
          semester: semesterId,
        }));
      
      return driveFiles;
    } catch (error) {
      console.error('Error fetching files in semester:', error);
      return [];
    }
  }

  // Search files across all semesters
  async searchFiles(query: string): Promise<DriveFile[]> {
    try {
      const headers = await this.getAuthHeaders();
      const folderId = APP_CONFIG.googleDriveFolderId;
      
      // Search for files containing the query in name, within the main folder
      const searchQuery = `'${folderId}'+in+parents+and+name+contains+'${query}'+and+mimeType!='application/vnd.google-apps.folder'`;
      const fields = 'files(id,name,mimeType,size,modifiedTime,webViewLink,thumbnailLink,parents)';
      
      const response = await fetch(
        `${this.baseUrl}/files?q=${encodeURIComponent(searchQuery)}&fields=${fields}&orderBy=name`,
        { headers }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to search files: ${response.statusText}`);
      }
      
      const data = await response.json();
      const files = data.files || [];
      
      // Get semester information for each file
      const driveFiles: DriveFile[] = await Promise.all(
        files
          .filter((file: any) => this.isSupportedFileType(file.mimeType))
          .slice(0, CONSTANTS.MAX_SEARCH_RESULTS)
          .map(async (file: any) => {
            const semesterName = await this.getSemesterNameForFile(file.parents[0]);
            return {
              id: file.id,
              name: file.name,
              mimeType: file.mimeType,
              size: file.size,
              modifiedTime: file.modifiedTime,
              webViewLink: file.webViewLink,
              thumbnailLink: file.thumbnailLink,
              semester: semesterName || 'Unknown',
            };
          })
      );
      
      return driveFiles;
    } catch (error) {
      console.error('Error searching files:', error);
      return [];
    }
  }

  // Get file download URL
  async getFileDownloadUrl(fileId: string): Promise<string | null> {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(
        `${this.baseUrl}/files/${fileId}?fields=webContentLink`,
        { headers }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to get download URL: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.webContentLink || null;
    } catch (error) {
      console.error('Error getting download URL:', error);
      return null;
    }
  }

  // Helper methods
  private async getFileCountInFolder(folderId: string): Promise<number> {
    try {
      const headers = await this.getAuthHeaders();
      const query = `'${folderId}'+in+parents+and+mimeType!='application/vnd.google-apps.folder'`;
      
      const response = await fetch(
        `${this.baseUrl}/files?q=${encodeURIComponent(query)}&fields=files(id)`,
        { headers }
      );
      
      if (!response.ok) return 0;
      
      const data = await response.json();
      return data.files?.length || 0;
    } catch (error) {
      return 0;
    }
  }

  private async getSemesterNameForFile(folderId: string): Promise<string | null> {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(
        `${this.baseUrl}/files/${folderId}?fields=name`,
        { headers }
      );
      
      if (!response.ok) return null;
      
      const data = await response.json();
      return data.name || null;
    } catch (error) {
      return null;
    }
  }

  private isSupportedFileType(mimeType: string): boolean {
    return CONSTANTS.SUPPORTED_FILE_TYPES.includes(mimeType);
  }

  // Caching methods
  private async cacheSemesters(semesters: Semester[]): Promise<void> {
    try {
      const cacheData = {
        semesters,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('cached_semesters', JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error caching semesters:', error);
    }
  }

  private async getCachedSemesters(): Promise<Semester[] | null> {
    try {
      const cached = await AsyncStorage.getItem('cached_semesters');
      if (!cached) return null;
      
      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > CONSTANTS.CACHE_DURATION;
      
      return isExpired ? null : cacheData.semesters;
    } catch (error) {
      console.error('Error getting cached semesters:', error);
      return null;
    }
  }
}

export default DriveApiService;
